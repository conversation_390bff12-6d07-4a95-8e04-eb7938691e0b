'use client';

import { formatCurrency } from '@/lib/utils';
import {
    ArrowUp,
    CheckCircle,
    Clock,
    Database,
    RefreshCw,
    Shield,
    Target,
    Zap
} from 'lucide-react';
import { useState } from 'react';

interface Property {
  id: string;
  address: string;
  suburb?: {
    name: string;
    state: string;
    postcode: string;
  };
  propertyType: string;
  bedrooms?: number;
  bathrooms?: number;
  carSpaces?: number;
  landSize?: number;
  buildingSize?: number;
  yearBuilt?: number;
  lastSalePrice?: number;
  lastSaleDate?: string;
  currentPrice?: number;
}

interface RealTimeValuationWidgetProps {
  property: Property;
  valuationHistory?: any;
}

export function RealTimeValuationWidget({ property, valuationHistory }: RealTimeValuationWidgetProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  // Mock real-time valuation data - in production this would come from API
  const valuationData = {
    currentValue: 1275000,
    confidenceScore: 87,
    confidenceRange: { min: 1225000, max: 1325000 },
    lastUpdated: lastUpdated,
    dataSourcesActive: 16,
    totalDataSources: 18,
    monthlyChange: { amount: 25000, percentage: 2.0 },
    
    // Key data sources
    dataSources: [
      'Recent Sales',
      'Market Trends', 
      'Property Features',
      'Location Score',
      'Infrastructure Data',
      'School Ratings'
    ],
    
    // Key insights
    keyInsights: [
      'Above suburb median by 15%',
      'Strong growth area (+8.2% YoY)',
      'Premium location score (89/100)',
      'High demand, low supply market'
    ],
    
    // AI Forecasting
    predictions: {
      12: { 
        value: 1387500, 
        confidence: 75,
        range: { min: 1325000, max: 1450000 }
      },
      24: { 
        value: 1512500, 
        confidence: 65,
        range: { min: 1425000, max: 1600000 }
      }
    }
  };

  const handleRefreshValuation = async () => {
    setIsRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setLastUpdated(new Date());
      setIsRefreshing(false);
    }, 2000);
  };

  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceBarColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-xl border border-gray-200 overflow-hidden">
      {/* Header with Live Indicator */}
      <div className="bg-white border-b border-gray-200 px-8 py-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-2xl font-bold text-gray-900">Real-Time Property Valuation</h1>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-600">LIVE</span>
              </div>
            </div>
            <p className="text-gray-600">
              Powered by Revalu's Event-Uplift Engine™ with 18+ data sources
            </p>
          </div>
          <button
            onClick={handleRefreshValuation}
            disabled={isRefreshing}
            className="flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Main Valuation Display */}
      <div className="px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Current Valuation */}
          <div className="lg:col-span-2">
            <div className="text-center lg:text-left">
              <div className="text-6xl font-bold text-gray-900 mb-3">
                {formatCurrency(valuationData.currentValue)}
              </div>
              <div className="text-lg text-gray-600 mb-4">
                Range: {formatCurrency(valuationData.confidenceRange.min)} - {formatCurrency(valuationData.confidenceRange.max)}
              </div>
              
              {/* Monthly Change */}
              <div className="flex items-center justify-center lg:justify-start space-x-4 mb-6">
                <div className="flex items-center text-green-600">
                  <ArrowUp className="w-5 h-5 mr-1" />
                  <span className="font-semibold text-lg">
                    +{formatCurrency(valuationData.monthlyChange.amount)} (+{valuationData.monthlyChange.percentage}%)
                  </span>
                </div>
                <span className="text-gray-500">since last month</span>
              </div>

              {/* Last Updated */}
              <div className="flex items-center justify-center lg:justify-start space-x-2 text-sm text-gray-600">
                <Clock className="w-4 h-4" />
                <span>Updated {getTimeAgo(valuationData.lastUpdated)}</span>
              </div>
            </div>
          </div>

          {/* Confidence Score & Data Sources */}
          <div className="space-y-6">
            {/* Confidence Score */}
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">Confidence Score</h3>
                <Shield className="w-5 h-5 text-gray-400" />
              </div>
              
              <div className="text-center">
                <div className={`text-3xl font-bold mb-2 ${getConfidenceColor(valuationData.confidenceScore)}`}>
                  {valuationData.confidenceScore}%
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3 mb-3">
                  <div 
                    className={`h-3 rounded-full transition-all duration-1000 ${getConfidenceBarColor(valuationData.confidenceScore)}`}
                    style={{ width: `${valuationData.confidenceScore}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-600">Very High Confidence</p>
              </div>
            </div>

            {/* Data Sources */}
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">Data Sources</h3>
                <Database className="w-5 h-5 text-gray-400" />
              </div>
              
              <div className="text-center mb-4">
                <div className="text-2xl font-bold text-primary-600">
                  {valuationData.dataSourcesActive}/{valuationData.totalDataSources}
                </div>
                <p className="text-sm text-gray-600">Active Sources</p>
              </div>

              <div className="space-y-2">
                {valuationData.dataSources.map((source, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-gray-700">{source}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Key Insights & AI Forecasting */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8 pt-8 border-t border-gray-200">
          {/* Key Insights */}
          <div>
            <div className="flex items-center space-x-2 mb-6">
              <Target className="w-5 h-5 text-primary-600" />
              <h3 className="text-xl font-semibold text-gray-900">Key Insights</h3>
            </div>

            <div className="space-y-4">
              {valuationData.keyInsights.map((insight, index) => (
                <div key={index} className="flex items-start space-x-3 p-4 bg-white rounded-lg border border-gray-200">
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <p className="text-gray-700 font-medium">{insight}</p>
                </div>
              ))}
            </div>
          </div>

          {/* AI Forecasting */}
          <div>
            <div className="flex items-center space-x-2 mb-6">
              <Zap className="w-5 h-5 text-primary-600" />
              <h3 className="text-xl font-semibold text-gray-900">AI Forecasting</h3>
            </div>

            <div className="space-y-4">
              {/* 12-Month Prediction */}
              <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-6 border border-blue-200">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-gray-900">12-Month Prediction</h4>
                  <div className="flex items-center text-green-600">
                    <ArrowUp className="w-4 h-4 mr-1" />
                    <span className="font-semibold">
                      +{((valuationData.predictions[12].value / valuationData.currentValue - 1) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>

                <div className="text-2xl font-bold text-gray-900 mb-2">
                  {formatCurrency(valuationData.predictions[12].value)}
                </div>

                <div className="text-sm text-gray-600 mb-3">
                  Range: {formatCurrency(valuationData.predictions[12].range.min)} - {formatCurrency(valuationData.predictions[12].range.max)}
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Confidence:</span>
                  <span className="font-semibold text-green-600">{valuationData.predictions[12].confidence}%</span>
                </div>
              </div>

              {/* 24-Month Prediction */}
              <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6 border border-purple-200">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-gray-900">24-Month Prediction</h4>
                  <div className="flex items-center text-green-600">
                    <ArrowUp className="w-4 h-4 mr-1" />
                    <span className="font-semibold">
                      +{((valuationData.predictions[24].value / valuationData.currentValue - 1) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>

                <div className="text-2xl font-bold text-gray-900 mb-2">
                  {formatCurrency(valuationData.predictions[24].value)}
                </div>

                <div className="text-sm text-gray-600 mb-3">
                  Range: {formatCurrency(valuationData.predictions[24].range.min)} - {formatCurrency(valuationData.predictions[24].range.max)}
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Confidence:</span>
                  <span className="font-semibold text-blue-600">{valuationData.predictions[24].confidence}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Property Summary Bar */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="bg-white rounded-lg p-6 border border-gray-200">
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{property.bedrooms || 'N/A'}</div>
                <div className="text-sm text-gray-600">Bedrooms</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{property.bathrooms || 'N/A'}</div>
                <div className="text-sm text-gray-600">Bathrooms</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{property.carSpaces || 'N/A'}</div>
                <div className="text-sm text-gray-600">Car Spaces</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{property.landSize || 'N/A'}</div>
                <div className="text-sm text-gray-600">Land Size (m²)</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{property.yearBuilt || 'N/A'}</div>
                <div className="text-sm text-gray-600">Year Built</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {property.lastSalePrice ? formatCurrency(property.lastSalePrice) : 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Last Sale</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
